package com.kedish.xyhelper_fox.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kedish.xyhelper_fox.cache.LocalCache;
import com.kedish.xyhelper_fox.component.ChatGptSessionAccessComponent;
import com.kedish.xyhelper_fox.component.ChatShareServerProxy;
import com.kedish.xyhelper_fox.component.ClaudeProxy;
import com.kedish.xyhelper_fox.exception.FoxException;
import com.kedish.xyhelper_fox.model.req.AddGptSessionReq;
import com.kedish.xyhelper_fox.model.req.PageQueryReq;
import com.kedish.xyhelper_fox.model.resp.Claude<PERSON>ar;
import com.kedish.xyhelper_fox.model.resp.GptCar;
import com.kedish.xyhelper_fox.model.resp.GrokCar;
import com.kedish.xyhelper_fox.repo.mapper.ChatGptSessionMapper;
import com.kedish.xyhelper_fox.repo.mapper.ClaudeSessionMapper;
import com.kedish.xyhelper_fox.repo.model.ChatGptSession;
import com.kedish.xyhelper_fox.repo.model.ChatgptUser;
import com.kedish.xyhelper_fox.repo.model.ClaudeSession;
import com.kedish.xyhelper_fox.repo.model.UserGroup;
import com.kedish.xyhelper_fox.security.UserContext;
import com.kedish.xyhelper_fox.utils.GptCarGenerator;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBatch;
import org.redisson.api.RFuture;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.NumberUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ChatGptSessionService {

    @Resource
    private ChatGptSessionMapper chatGptSessionMapper;

    @Resource
    private ChatGptConfigService chatGptConfigService;

    private volatile List<GptCar> nodeFree = new ArrayList<>();
    private volatile List<GptCar> node4o = new ArrayList<>();

    private volatile List<GptCar> nodePlus = new ArrayList<>();

    private volatile List<GptCar> nodeVirtualPlus = new ArrayList<>();

    private volatile List<ClaudeCar> nodeClaude = new ArrayList<>();

    private volatile List<ClaudeCar> nodeVirtualClaude = new ArrayList<>();

    private volatile List<GrokCar> nodeGrok = new ArrayList<>();

    private volatile List<GrokCar> nodeVirtualGrok = new ArrayList<>();

    @Resource
    private LocalCache localCache;

    @Resource
    private RedissonClient redissonClient;
    private Map<String, GptCarGenerator.HeatConfig>
            heatConfigMap = new HashMap<>();
    @Resource
    private ChatShareServerProxy chatShareServerProxy;

    @Resource
    private ClaudeSessionMapper claudeSessionMapper;
    @Resource
    private ChatGptSessionAccessComponent chatGptSessionAccessComponent;

    @Resource
    private UserGroupService userGroupService;

    @Resource
    private ClaudeProxy claudeProxy;

    private volatile String currentHeat;

    @PostConstruct
    public void init() {
        GptCarGenerator.HeatConfig lowHeat = new GptCarGenerator.HeatConfig(80, 15, 5);
        GptCarGenerator.HeatConfig mediumHeat = new GptCarGenerator.HeatConfig(30, 60, 10);
        GptCarGenerator.HeatConfig highHeat = new GptCarGenerator.HeatConfig(10, 10, 80);
        heatConfigMap.put("low", lowHeat);
        heatConfigMap.put("medium", mediumHeat);
        heatConfigMap.put("high", highHeat);

        generateVirtualPlus();
        generateVirtualClaude();
        generateVirtualGrok();
        refreshNode();
    }


    public synchronized void applyVirtualPlusSizeChange() {
        int virtualPlusSize = NumberUtils.parseNumber(localCache.getConfigMap().get("virtualPlusSize"), Integer.class);
        if (virtualPlusSize == 0) {
            nodeVirtualPlus.clear();
            log.info("虚拟Plus数量为0，已清空");
            nodePlus.removeIf(GptCar::isVirtual);
            return;
        }
        int newVirtualPlusSize = virtualPlusSize - nodeVirtualPlus.size();
        if (newVirtualPlusSize == 0) {

            return;
        }
        if (newVirtualPlusSize > 0) {
            Map<String, String> configMap = localCache.getConfigMap();
            List<GptCar> gptCars = GptCarGenerator.generateGptCars(newVirtualPlusSize,
                    heatConfigMap.get(configMap.getOrDefault("virtualPlusHeat", "high")));
            nodeVirtualPlus.addAll(gptCars);
            nodePlus.removeIf(GptCar::isVirtual);
            nodePlus.addAll(nodeVirtualPlus);
            log.info("虚拟Plus数量为{}，已生成{}个", virtualPlusSize, newVirtualPlusSize);
        } else {
            int removeCount = Math.abs(newVirtualPlusSize);
            if (removeCount > nodeVirtualPlus.size()) {
                removeCount = nodeVirtualPlus.size();
            }
            //随机移除
            for (int i = 0; i < removeCount; i++) {
                GptCar gptCar = randomPickOne(nodeVirtualPlus);
                nodeVirtualPlus.remove(gptCar);
            }
            nodePlus.removeIf(GptCar::isVirtual);
            nodePlus.addAll(nodeVirtualPlus);
            log.info("虚拟Plus数量为{}，已移除{}个", virtualPlusSize, removeCount);
        }
    }

    public synchronized void applyVirtualPlusHeatChange() {

        String s = localCache.getConfigMap().get("virtualPlusHeat");
        if (s == null) {
            s = "high";
        }
        if (s.equals(currentHeat)) {
            log.info("虚拟Plus热力配置未变化，不执行更新");
            return;
        }
        currentHeat = s;
        GptCarGenerator.HeatConfig heatConfig = heatConfigMap.get(s);
        GptCarGenerator.updateHeat(nodeVirtualPlus, heatConfig);
        log.info("虚拟Plus热力配置已更新为{}", s);
    }

    public synchronized void applyFreeSizeChange() {
        int freeSize = NumberUtils.parseNumber(localCache.getConfigMap().get("nodeFreeSize"), Integer.class);
        if (freeSize == 0) {
            nodeFree.clear();
            return;
        }
        nodeFree = node4o.stream().sorted(Comparator.comparing(GptCar::getCarID))
                .limit(freeSize).toList();
//        nodeFree
    }

    private void generateVirtualPlus() {


        Map<String, String> configMap = chatGptConfigService.getConfigMap(Arrays.asList("virtualPlusSize", "virtualPlusHeat"));
        String heat = "medium";
        int virtualPlusSize = 0;
        if (configMap.containsKey("virtualPlusSize")) {
            virtualPlusSize = NumberUtils.parseNumber(configMap.get("virtualPlusSize"), Integer.class);
        }
        if (configMap.containsKey("virtualPlusHeat")) {
            heat = configMap.get("virtualPlusHeat");
        }
        currentHeat = heat;
        if (virtualPlusSize > 0) {

            List<GptCar> gptCars = GptCarGenerator.generateGptCars(virtualPlusSize, heatConfigMap.get(heat));
            nodeVirtualPlus = gptCars;
        }
    }

    private void generateVirtualGrok() {
        Map<String, String> configMap = chatGptConfigService.getConfigMap(Collections.singletonList("grokVirtualSize"));
        int virtualSize = 0;
        if (configMap.containsKey("grokVirtualSize")) {
            virtualSize = NumberUtils.parseNumber(configMap.get("grokVirtualSize"), Integer.class);
        }

        if (virtualSize > 0) {
            List<GptCar> gptCars = GptCarGenerator.generateGptCars(virtualSize, heatConfigMap.get("high"));
            nodeVirtualGrok = gptCars.stream().map(GptCar::toGrokCar).collect(Collectors.toList());
        }
    }

    private void generateVirtualClaude() {
        Map<String, String> configMap = chatGptConfigService.getConfigMap(Collections.singletonList("claudeVirtualSize"));
        int virtualSize = 0;
        if (configMap.containsKey("claudeVirtualSize")) {
            virtualSize = NumberUtils.parseNumber(configMap.get("claudeVirtualSize"), Integer.class);
        }

        if (virtualSize > 0) {
            List<GptCar> gptCars = GptCarGenerator.generateGptCars(virtualSize, heatConfigMap.get("high"));
            nodeVirtualGrok = gptCars.stream().map(GptCar::toGrokCar).collect(Collectors.toList());
        }
    }

    public Page<ChatGptSession> page(PageQueryReq req) {
        Page<ChatGptSession> page = new Page<>(req.getPageNum(), req.getPageSize());
        if (StringUtils.hasLength(req.getSortField()) && StringUtils.hasLength(req.getSortOrder())) {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(req.getSortField());
            orderItem.setAsc(req.getSortOrder().equals("asc"));
            page.addOrder(orderItem);

        }
        QueryWrapper<ChatGptSession> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("deleted_at");
        if (StringUtils.hasLength(req.getQuery()))
            queryWrapper.and(wrapper -> wrapper.like("remark", req.getQuery())
                    .or()
                    .like("email", req.getQuery()));
        return chatGptSessionMapper.selectPage(
                page, queryWrapper
        );
    }

    public List<?> carList(String type) {
        List<GptCar> result = Collections.emptyList();
        if ("free".equals(type)) {
            if (nodeFree != null) {

                result = new ArrayList<>(nodeFree);
            }

        } else if ("4o".equals(type)) {
            if (node4o != null) {

                result = new ArrayList<>(node4o);
            }
        } else if ("plus".equals(type)) {
            if (nodePlus != null) {

                result = new ArrayList<>(nodePlus);
            }
        } else if ("claude".equals(type)) {
            return new ArrayList<>(nodeClaude);
        } else if ("grok".equals(type)) {
            return new ArrayList<>(nodeGrok);
        }
        if ("free".equals(type) || "4o".equals(type) || "plus".equals(type)) {
            try {
                long start = System.currentTimeMillis();
                fillClearsIn((List<GptCar>) result);
                long end = System.currentTimeMillis();
                log.info("fillClearsIn cost:{} ms", end - start);

            } catch (ExecutionException | InterruptedException e) {
                log.error("fillClearsIn error", e);
            }

            //根据clearsin 和 count 进行排序
            if (!result.isEmpty()) {

                result.sort(Comparator.comparing(GptCar::getClearsIn).thenComparing(GptCar::getCount));
            }
        }

        return result;
    }

    private void fillClearsIn(List<GptCar> carList) throws ExecutionException, InterruptedException {
        if (!CollectionUtils.isEmpty(carList)) {
            List<GptCar> noVirtual = carList.stream().filter(item -> !item.isVirtual()).toList();
            List<String> keys = noVirtual.stream().map(item -> "clears_in:" + item.getCarID()).toList();
            RBatch batch = redissonClient.createBatch();
            Map<String, RFuture<String>> futures = new HashMap<>();

            // 将所有get请求添加到批处理中
            for (String key : keys) {
                RFuture<String> future = batch.<String>getBucket(key, StringCodec.INSTANCE).getAsync();
                futures.put(key, future);
            }

            // 执行批处理
            batch.execute();

            for (GptCar car : noVirtual) {
                RFuture<String> future = futures.get("clears_in:" + car.getCarID());
                String value = future.toCompletableFuture().get();
                if (value != null) {
                    car.setClearsIn(Integer.parseInt(value));
                }
            }
        }
    }

    @Scheduled(fixedRate = 10, timeUnit = TimeUnit.MINUTES)
    public void refreshCarLabel() {
        log.info("start refresh carLabel ...");
        if (!CollectionUtils.isEmpty(nodePlus)) {
            for (GptCar plus : nodePlus) {
                fillCarLabel(plus);
            }
        }
        log.info("refresh carLabel done...");
    }

    public void fillCarLabel(GptCar car) {
        String carLabel = chatShareServerProxy.getCarLabel(car.getCarID());

        if (StringUtils.hasLength(carLabel)) {
            car.setLabel(carLabel);
        }
    }

    @Scheduled(fixedRate = 1, timeUnit = TimeUnit.MINUTES)
    public void refreshNode() {
        refreshGptNode(null);

        boolean enableClaude = Boolean.parseBoolean(localCache.getConfigMap().get("enableClaude"));
        if (enableClaude) {
            refreshClaudeNode();
        }

        boolean enableGrok = Boolean.parseBoolean(localCache.getConfigMap().get("enableGrok"));
        if (enableGrok) {
            refreshGrokNode();
        }
    }

    private void refreshGptNode(List<String> needCheckLabel) {
        log.info("load all car list");
        List<ChatGptSession> carList = loadAll();
        log.info("loaded , car list size:{}", carList.size());


        int nodeFreeSize = 0;
        int virtualPlusSize = 0;
        Map<String, String> configMap = localCache.getConfigMap();

        try {
            nodeFreeSize = Integer.parseInt(configMap.get("nodeFreeSize"));
            virtualPlusSize = Integer.parseInt(configMap.get("virtualPlusSize"));
        } catch (Exception e) {
            log.error("load config error", e);
        }

        if (!CollectionUtils.isEmpty(carList)) {
            List<ChatGptSession> gpt4o = carList.stream().filter(item -> item.getIsPlus() == 0).toList();
            List<ChatGptSession> gptPlus = carList.stream().filter(item -> item.getIsPlus() == 1).toList();
            if (nodeFreeSize > 0 && !gpt4o.isEmpty()) {
                nodeFree = gpt4o.stream().limit(nodeFreeSize).map(GptCar::fromChatGptSession).toList();
                log.info("nodeFree size:{}", nodeFree.size());
            }
            node4o = gpt4o.stream().map(GptCar::fromChatGptSession).toList();
            log.info("node4o size:{}", node4o.size());

            List<GptCar> preNodePlus = nodePlus;
            nodePlus = gptPlus.stream().map(GptCar::fromChatGptSession).collect(Collectors.toList());
            log.info("nodePlus size:{}", nodePlus.size());

            if (!CollectionUtils.isEmpty(preNodePlus)){
                //继承 LABEL
                for (GptCar car : preNodePlus) {
                    Optional<GptCar> any = nodePlus.stream().filter(item -> item.getCarID().equals(car.getCarID())).findAny();
                    any.ifPresent(gptCar -> gptCar.setLabel(car.getLabel()));
                }
            }
            //填充车队使用次数
            fillCarUseSize(nodePlus);
            fillCarUseSize(node4o);
            fillCarUseSize(nodeFree);
            if (!nodePlus.isEmpty() && virtualPlusSize > 0 && nodeVirtualPlus != null) {
                //添加虚拟车队
                nodePlus.addAll(nodeVirtualPlus);

            }
            //填充车队label
            if (needCheckLabel != null && !needCheckLabel.isEmpty()) {
                for (GptCar car : nodePlus) {
                    if (needCheckLabel.contains(car.getCarID())) {
                        fillCarLabel(car);
                    }
                }
            }


        } else {
            nodeFree = Collections.emptyList();
            node4o = Collections.emptyList();
            nodePlus = new ArrayList<>();
        }

    }

    public void refreshClaudeNode() {
        String claudeUrl = localCache.getConfigMap().get("claudeUrl");

        if (StringUtils.hasLength(claudeUrl)) {

            List<ClaudeSession> claudeSessions = loadAllClaude();
            if (!CollectionUtils.isEmpty(claudeSessions)) {
                nodeClaude = claudeSessions.stream().map(ClaudeCar::fromClaudeSession).collect(Collectors.toList());
                log.info("nodeClaude size:{}", nodeClaude.size());
            }
        } else {
            String lyyClaudeUrl = localCache.getConfigMap().get("lyyClaudeUrl");
            if (StringUtils.hasLength(lyyClaudeUrl)) {
                nodeClaude = claudeProxy.carPage();
                log.info("nodeClaude size:{}", nodeClaude.size());
            }
        }
    }

    public void refreshGrokNode() {
        String grokUrl = localCache.getConfigMap().get("grokUrl");
        if (StringUtils.hasLength(grokUrl)) {

            // 添加虚拟车队
            int grokVirtualSize = NumberUtils.parseNumber(localCache.getConfigMap().getOrDefault("grokVirtualSize", "0"), Integer.class);
            if (grokVirtualSize > 0 && nodeVirtualGrok != null) {
                nodeGrok = (nodeVirtualGrok);
            }

            log.info("nodeGrok size:{}", nodeGrok.size());
        }
    }

    private void fillCarUseSize(List<GptCar> gptCars) {
        if (!CollectionUtils.isEmpty(gptCars)) {
            List<String> carIds = gptCars.stream().map(GptCar::getCarID).toList();
            Map<String, Integer> accessCountMap;
            try {
                accessCountMap = chatGptSessionAccessComponent.getByCarIds(carIds);

                for (GptCar gptCar : gptCars) {
                    gptCar.setCount(accessCountMap.getOrDefault(gptCar.getCarID(), 0));
                    //0~10 空闲
                    if (gptCar.getCount() < 10) {
                        gptCar.setDesc("空闲");
                    } else if (gptCar.getCount() < 20) {
                        gptCar.setDesc("推荐");
                    } else if (gptCar.getCount() < 30) {
                        gptCar.setDesc("拥挤");
                    } else {
                        gptCar.setDesc("繁忙");
                    }
                }
            } catch (ExecutionException e) {
                log.error("fillCarUseSize error", e);
            }

        }
    }

    private List<ChatGptSession> loadAll() {
        QueryWrapper<ChatGptSession> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("deleted_at")
                .eq("status", 1);
        return chatGptSessionMapper.selectList(queryWrapper);
    }

    private List<ClaudeSession> loadAllClaude() {
        QueryWrapper<ClaudeSession> queryWrapper = new QueryWrapper<>();
        return claudeSessionMapper.selectList(queryWrapper);
    }

    public boolean addChatGptSession(AddGptSessionReq req) {
        String s = chatShareServerProxy.addChatGptSession(req);
        log.info("addChatGptSession result:{}", s);
        checkShareServerResult(s);

        refreshGptNode(Collections.singletonList(req.getCarID()));
        return true;
    }

    public boolean updateChatGptSession(AddGptSessionReq req) {
        String s = chatShareServerProxy.updateChatGptSession(req);
        log.info("updateChatGptSession result:{}", s);
        checkShareServerResult(s);

        refreshGptNode(Collections.singletonList(req.getCarID()));
        return true;
    }

    public boolean deleteChatGptSession(List<Long> ids) {
        String s = chatShareServerProxy.deleteChatGptSession(ids);
        log.info("deleteChatGptSession result:{}", s);
        checkShareServerResult(s);
        refreshGptNode(null);
        return true;
    }

    private void checkShareServerResult(String result) {
        //{code: 1000, message: "BaseResMessage", data: {Locker: {}}}
        JSONObject jsonObject = JSON.parseObject(result);
        if (jsonObject.getInteger("code") != 1000) {
            throw new FoxException(jsonObject.getString("message"));
        }
    }

    private <T> T randomPickOne(List<T> list) {
        return list.get(new Random().nextInt(list.size()));
    }

    public void checkUserRights(String carId, String type) {
        ChatgptUser user = UserContext.getUser();
        UserGroup group = userGroupService.getById(user.getGroupId());
        LocalDateTime plusExpireTime = user.getPlusExpireTime();
        LocalDateTime now = LocalDateTime.now();
        if (!group.getAvailableNodes().contains(type)) {
            throw new FoxException("car.no.permission", null);
        }
        // 游客不需要校验过期
        if (user.isVisitor()) {
            return;
        }
        if ("plus".equals(group.getName())) {
            if (plusExpireTime.isAfter(now)) {
                return;
            } else {
                throw new FoxException("car.plus.expired", null);
            }
        } else if ("general".equals(group.getName())) {
            //优先校验plusExpireTime
            if (plusExpireTime.isAfter(now)) {
                return;
            }

            LocalDateTime expireTime = user.getExpireTime();
            if (expireTime.isAfter(now)) {
                return;
            } else {
                throw new FoxException("car.general.expired", null);
            }
        }
    }

    public String selectCarId(String carId, String type) {
        if ("free".equals(type)) {
            if (nodeFree.isEmpty()) {
                return null;
            }
            Optional<GptCar> any = nodeFree.stream().filter(item -> item.getCarID().equals(carId))
                    .findAny();
            if (any.isPresent()) {
                return carId;
            }
            return randomPickOne(nodeFree).getCarID();
        } else if ("4o".equals(type)) {
            if (node4o.isEmpty()) {
                return null;
            }
            return node4o.stream().filter(item -> item.getCarID().equals(carId))
                    .findAny().orElse(randomPickOne(node4o)).getCarID();
        } else if ("plus".equals(type)) {
            if (nodePlus.isEmpty()) {
                return null;
            }
            List<GptCar> noVirtual = nodePlus.stream().filter(item -> !item.isVirtual()).toList();
            return noVirtual.stream().filter(item -> item.getCarID().equals(carId))
                    .filter(item -> !item.isVirtual())
                    .findAny().orElse(randomPickOne(noVirtual)).getCarID();
        } else if ("claude".equals(type)) {
            if (nodeClaude.isEmpty()) {
                return null;
            }
            return nodeClaude.stream().filter(item -> item.getCarID().equals(carId))
                    .findAny().orElse(randomPickOne(nodeClaude)).getCarID();
        } else if ("grok".equals(type)) {
            if (nodeGrok.isEmpty()) {
                return null;
            }
            List<GrokCar> noVirtual = nodeGrok.stream().filter(item -> !item.isVirtual()).toList();
            return noVirtual.stream().filter(item -> item.getCarID().equals(carId))
                    .filter(item -> !item.isVirtual())
                    .findAny().orElse(randomPickOne(noVirtual)).getCarID();
        }
        return null;
    }

    public String generateCarId() {
        return GptCarGenerator.generateCarId();
    }


    public synchronized void applyClaudeVirtualSizeChange() {
        int virtualPlusSize = NumberUtils.parseNumber(localCache.getConfigMap().get("claudeVirtualSize"), Integer.class);
        if (virtualPlusSize == 0) {
            nodeVirtualClaude.clear();
            log.info("虚拟Plus数量为0，已清空");
            nodeClaude.removeIf(ClaudeCar::isVirtual);
            return;
        }
        int newVirtualPlusSize = virtualPlusSize - nodeVirtualClaude.size();
        if (newVirtualPlusSize == 0) {

            return;
        }
        if (newVirtualPlusSize > 0) {
            Map<String, String> configMap = localCache.getConfigMap();
            List<ClaudeCar> claudeCars = GptCarGenerator.generateGptCars(newVirtualPlusSize,
                            heatConfigMap.get(configMap.getOrDefault("virtualPlusHeat", "high")))
                    .stream().map(GptCar::toClaudeCar).toList();
            nodeVirtualClaude.addAll(claudeCars);
            nodeClaude.removeIf(ClaudeCar::isVirtual);
            nodeClaude.addAll(nodeVirtualClaude);
            log.info("claude虚拟车队数量为{}，已生成{}个", virtualPlusSize, newVirtualPlusSize);
        } else {
            int removeCount = Math.abs(newVirtualPlusSize);
            if (removeCount > nodeVirtualClaude.size()) {
                removeCount = nodeVirtualClaude.size();
            }
            //随机移除
            for (int i = 0; i < removeCount; i++) {
                ClaudeCar gptCar = randomPickOne(nodeVirtualClaude);
                nodeVirtualClaude.remove(gptCar);
            }
            nodeClaude.removeIf(ClaudeCar::isVirtual);
            nodeClaude.addAll(nodeVirtualClaude);
            log.info("claude虚拟车队数量为{}，已移除{}个", virtualPlusSize, removeCount);
        }
    }

    public synchronized void applyGrokVirtualSizeChange() {
        int virtualSize = NumberUtils.parseNumber(localCache.getConfigMap().get("grokVirtualSize"), Integer.class);
        if (virtualSize == 0) {
            nodeVirtualGrok.clear();
            log.info("Grok虚拟车队数量为0，已清空");
            nodeGrok.removeIf(GrokCar::isVirtual);
            return;
        }

        int newVirtualSize = virtualSize - nodeVirtualGrok.size();
        if (newVirtualSize == 0) {
            return;
        }

        if (newVirtualSize > 0) {
            Map<String, String> configMap = localCache.getConfigMap();
            List<GrokCar> grokCars = GptCarGenerator.generateGptCars(newVirtualSize,
                            heatConfigMap.get(configMap.getOrDefault("virtualPlusHeat", "high")))
                    .stream().map(GptCar::toGrokCar).toList();
            nodeVirtualGrok.addAll(grokCars);
            nodeGrok.removeIf(GrokCar::isVirtual);
            nodeGrok.addAll(nodeVirtualGrok);
            log.info("Grok虚拟车队数量为{}，已生成{}个", virtualSize, newVirtualSize);
        } else {
            int removeCount = Math.abs(newVirtualSize);
            if (removeCount > nodeVirtualGrok.size()) {
                removeCount = nodeVirtualGrok.size();
            }
            //随机移除
            for (int i = 0; i < removeCount; i++) {
                GrokCar grokCar = randomPickOne(nodeVirtualGrok);
                nodeVirtualGrok.remove(grokCar);
            }
            nodeGrok.removeIf(GrokCar::isVirtual);
            nodeGrok.addAll(nodeVirtualGrok);
            log.info("Grok虚拟车队数量为{}，已移除{}个", virtualSize, removeCount);
        }
    }
}
