<template>
    <el-tooltip content="在这里配置系统的基本设置" placement="top">
        <el-alert title="系统配置" type="info" :closable="false"></el-alert>
    </el-tooltip>
    <el-form :model="form" label-width="auto" style="max-width: 500px;margin-top: 20px;">
        <el-form-item label="授权状态">
            <el-tag v-if="authStatus.expireTime" type="success">已授权</el-tag>
            <el-tag v-else type="danger">未授权</el-tag>
        </el-form-item>
        <el-form-item label="授权过期时间">
            <el-input v-model="authStatus.expireTime" disabled></el-input>
        </el-form-item>
        <el-form-item label="系统版本">
            <el-input v-model="authStatus.ver" disabled></el-input>
        </el-form-item>
        <el-form-item style="justify-content: flex-end;">
            <div style="justify-content: flex-end;">
                <el-button type="primary" @click="refreshAuth">刷新授权</el-button>
            </div>
        </el-form-item>
    </el-form>
</template>

<script setup>
import { reactive, onMounted } from 'vue'
import api from '@/axios'
import { ElMessage } from 'element-plus'
const authStatus = reactive({
    expireTime: '',
    ver: ''
})
const getAuthStatus = async () => {
    const res = await api.get('/api/auth/status')
    if (res.data.code === 0) {
        authStatus.expireTime = res.data.data.expireTime
        authStatus.ver = res.data.data.ver
    }
}
const refreshAuth = async () => {
    const res = await api.get('/api/auth/refresh')
    if (res.data.code === 0) {
        ElMessage.success('刷新成功')
        getAuthStatus()
    }
}
onMounted(() => {
    getAuthStatus()
})
</script>
