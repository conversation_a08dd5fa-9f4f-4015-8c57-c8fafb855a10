import {createApp} from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

import './style.css'
//引入所有图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import router from './route'
import {createPinia} from 'pinia'
import axios from 'axios'
import moment from 'moment'
// 引入国际化
import i18n from './i18n'

const app = createApp(App)
app.directive('richText', {
    mounted(el, binding) {
      const shadowRoot = el.attachShadow({ mode: 'closed' })
      let htmlText, styleText
      if (typeof binding.value === 'object') {
        htmlText = binding.value.htmlText
        styleText = binding.value.styleText
      } else {
        htmlText = binding.value
      }
      shadowRoot.innerHTML = htmlText
      if (styleText) {
        // 给富文本添加样式
        const style = document.createElement('style')
        style.textContent = styleText
        shadowRoot.appendChild(style)
      }
    },

})
app.config.globalProperties.$dateFormat = function (date, format='yyyy-MM-DD HH:mm') {
    return moment(date).format(format)
}
app.config.globalProperties.$getLatestNotification = function (arr,prop) {
  if (!arr) {
    return null
  }
  let propName = 'createdAt'
  if (prop) {
    propName = prop
  }
   arr.sort((a, b) => {
    return new Date(b[propName]) - new Date(a[propName])
  })
  return arr[0]
}

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}


const pinia = createPinia()
app.use(pinia)
app.use(ElementPlus)
app.use(router)
app.use(i18n)
app.mount('#app')
