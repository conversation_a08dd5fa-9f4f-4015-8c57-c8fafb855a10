import {createRouter, createWebHistory,createWebHashHistory} from 'vue-router';
// import Login from './components/login.vue';
import ResetPassword from './components/oaiLogin/resetPassword.vue';
import Register from './components/oaiLogin/register.vue';
import Home from './components/Home.vue';
import backend from './components/backend/backend.vue';
import sysconfig from './components/backend/sysconfig.vue';
import email_config from './components/backend/email_config.vue';
import member from './components/backend/member.vue';
import useUserStore from './store/user';
import userManage from './components/backend/userManage.vue';
import login2 from './components/login2.vue';
import login from './components/oaiLogin/login.vue'
import notice from './components/backend/notice.vue';
import carSetting from './components/backend/carSetting.vue';
import gptSession from './components/backend/gptSession.vue';
import forbiddenWord from './components/backend/forbiddenWord.vue';
import salesPlan from './components/backend/salesPlan.vue';
import purchase from './components/purchase.vue';
import conpon from './components/backend/conpon.vue';
import ClaudeNodeTabs from './components/ClaudeNodeTabs.vue';
import paymethod from './components/backend/paymethod.vue';
import order from './components/backend/order.vue';
import useNote from './components/useNote.vue'
import NodeTabs from './components/NodeTabs.vue';
import claudeSession from './components/backend/claudeSession.vue';
import lyyClaudeSession from './components/backend/lyyClaudeSession.vue';
import conversation from './components/backend/conversation.vue';
import activationCode from './components/backend/activationCode.vue';
import exchange from './components/exchange.vue';
import userGroupManage from './components/backend/userGroupManage.vue';
import drawRecord from './components/backend/drawRecord.vue';
import paint from './components/draw/paint.vue';
import UserCenter from './components/draw/UserCenter.vue';
import MyWorks from './components/draw/MyWorks.vue';
import drawConfig from './components/backend/drawConfig.vue';
import GrokNodeTabs from './components/GrokNodeTabs.vue';
import grokSession from './components/backend/grokSession.vue';
import GalleryDemo from './views/GalleryDemo.vue';
import SmartGalleryDemo from './views/SmartGalleryDemo.vue';
import todayDashboard from './components/backend/todayDashboard.vue';
import userDashboard from './components/backend/userDashboard.vue';
import orderDashboard from './components/backend/orderDashboard.vue';
import Auth from './components/backend/Auth.vue';
const routes = [
    {path: '/NodeTabs', component: NodeTabs},
    {path: '/login', component: login},
    // {path: '/login2', component: login2},
    {path: '/reset-password', component: ResetPassword},
    {path: '/userCenter', component: UserCenter},
    {path: '/myWorks', component: MyWorks},
    {path: '/home', component: Home,
      children: [{
        path: '/purchase',
        component: purchase
      },
      {
        path: '/carPage',
        component: NodeTabs,
        props: true
      },
      {
        path: '/claudeCarPage',
        component: ClaudeNodeTabs,
        props: true
      },
      {
        path: '/grokCarPage',
        component: GrokNodeTabs,
        props: true
      },
      {
        path: '/useNote',
        component: useNote
      },
      {
        path: '/exchange',
        component: exchange
      },
      {
        path: '/paint',
        component: paint
      },
      {
        path: '/gallery-demo',
        component: GalleryDemo
      },
      {
        path: '/smart-gallery-demo',
        component: SmartGalleryDemo
      }
    ]
    },
    {path: '/', redirect: '/home'},
    {path: '/register', component: Register},
    {path: '/fox-backend', component: backend,
      
      children: [{
        path: '/userManage',
        component: userManage
      },
        {
          path: '/sysconfig',
          component: sysconfig
        },
        {
          path: '/email_config',
          component: email_config
        },
        {
          path: '/member',
          component: member
        

        },
        {
          path: '/notice',
          component: notice
        },
        {
          path: '/carSetting',
          component: carSetting
        },
        {
          path: '/gptSession',
          component: gptSession
        },
        {
          path: '/forbiddenWord',
          component: forbiddenWord
        },
        {
          path: '/salesPlan',
          component: salesPlan
        },
        {
          path: '/conpon',
          component: conpon
        },
        {
          path: '/paymethod',
          component: paymethod
        },
        {
          path: '/order',
          component: order
        },
        {
          path: '/claudeSession',
          component: claudeSession
        },
        {
          path: '/lyyClaudeSession',
          component: lyyClaudeSession
        },
        {
          path: '/grokSession',
          component: grokSession
        },
        {
          path: '/conversation',
          component: conversation
        },
        {
          path: '/activationCode',
          component: activationCode
        },
        {
          path: '/dashboard',
          component: todayDashboard
        },
        {
          path: '/dashboard/today',
          component: todayDashboard
        },
        {
          path: '/dashboard/user',
          component: userDashboard
        },
        {
          path: '/dashboard/order',
          component: orderDashboard
        },
        {
          path: '/userGroupManage',
          component: userGroupManage
        },
        {
          path: '/drawRecord',
          component: drawRecord
        },
        {
          path: '/drawConfig',
          component: drawConfig
        },
        {
          path: '/auth',
          component: Auth
        }
      ]
    }
];


const router = createRouter({
    history: createWebHashHistory(),
    routes
})
router.beforeEach(async (to, from, next) => {
    const userStore = useUserStore();
    if (userStore.token && !userStore.user) {
      // 如果有 token 但没有 user 数据，尝试获取用户信息
      await userStore.init()
    }
    next();
  });
export default router
